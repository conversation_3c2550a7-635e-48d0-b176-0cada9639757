'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Button } from '@/modules/ui/components/button'
import { Badge } from '@/modules/ui/components/badge'
import { Input } from '@/modules/ui/components/input'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/modules/ui/components/sheet'
import {
  LoaderIcon,
  SearchIcon,
  PackageIcon,
  CheckIcon,
  AlertCircleIcon,
  ExternalLinkIcon,
  TagIcon,
  ZapIcon,
  PlusIcon
} from 'lucide-react'
import { toast } from 'sonner'
import { useCaktoProducts } from '../../hooks/useCaktoProducts'

interface CaktoProduct {
  id: string
  name: string
  description?: string
  image?: string
  price: number
  type: 'course' | 'ebook' | 'mentorship' | string
  status: 'active' | 'inactive' | string
  organizationId?: string
  contentDeliveries: string[]
  alreadyLinked: boolean
}

interface CaktoProductSelectorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductSelect?: (product: CaktoProduct) => void
}

// Helper function to format price
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(price)
}

export function CaktoProductSelector({ open, onOpenChange, onProductSelect }: CaktoProductSelectorProps) {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProduct, setSelectedProduct] = useState<CaktoProduct | null>(null)

  const { products, isLoading, error } = useCaktoProducts({ enabled: open })

  const filteredProducts = products.filter((product: CaktoProduct) =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.description?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  )

  const handleProductSelect = (product: CaktoProduct) => {
    if (product.alreadyLinked) {
      toast.error('Este produto já está associado a outro curso')
      return
    }
    setSelectedProduct(product)
  }

  const handleConfirmSelection = () => {
    if (!selectedProduct) {
      toast.error('Selecione um produto')
      return
    }

    if (onProductSelect) {
      // Callback para seleção direta
      onProductSelect(selectedProduct)
      onOpenChange(false)
      setSelectedProduct(null)
      setSearchTerm('')
    } else {
      // Navegar para página de criar produto
      router.push(`/app/admin/courses/create?caktoProductId=${selectedProduct.id}&caktoProductName=${encodeURIComponent(selectedProduct.name)}`)
    }
  }

  const handleCreateWithProduct = () => {
    if (!selectedProduct) {
      toast.error('Selecione um produto')
      return
    }

    // Navegar para página de criar produto
    router.push(`/app/admin/courses/create?caktoProductId=${selectedProduct.id}&caktoProductName=${encodeURIComponent(selectedProduct.name)}`)
  }

  const handleCancel = () => {
    setSelectedProduct(null)
    setSearchTerm('')
    onOpenChange(false)
  }

  // Don't render anything if sheet is not open
  if (!open) {
    return null
  }

  if (error) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent className="sm:max-w-[600px]">
          <SheetHeader>
            <SheetTitle>Erro ao carregar produtos</SheetTitle>
            <SheetDescription>
              Não foi possível carregar os produtos da Cakto. Tente novamente.
            </SheetDescription>
          </SheetHeader>
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <AlertCircleIcon className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <p className="text-sm text-gray-600">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
                variant="outline"
              >
                Tentar novamente
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    )
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-[500px] sm:w-[600px] flex flex-col">
        <SheetHeader className="pb-4 flex-shrink-0">
          <SheetTitle className="flex items-center gap-2 text-lg">
            <PackageIcon className="h-5 w-5 text-blue-600" />
            Selecionar Produto da Cakto
          </SheetTitle>
          <SheetDescription className="text-sm">
            Escolha um produto da Cakto para associar ao curso. Apenas produtos com entrega via Cakto são exibidos.
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 flex flex-col gap-3 min-h-0">
          {/* Search */}
          <div className="relative flex-shrink-0">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Buscar produtos por nome ou descrição..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-10 text-sm"
            />
          </div>

          {/* Products List */}
          <div className="flex-1 overflow-y-auto min-h-0">
            {isLoading ? (
              <div className="flex items-center justify-center p-8">
                <div className="text-center">
                  <LoaderIcon className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-3" />
                  <p className="text-sm text-gray-600">Carregando produtos da Cakto...</p>
                </div>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center p-8">
                <PackageIcon className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                <p className="text-sm text-gray-600 mb-1">
                  {searchTerm ? 'Nenhum produto encontrado para sua busca.' : 'Nenhum produto disponível.'}
                </p>
                {searchTerm && (
                  <p className="text-xs text-gray-500">Tente ajustar os termos de busca</p>
                )}
              </div>
            ) : (
              <div className="space-y-2 pb-2">
                {filteredProducts.map((product: CaktoProduct) => (
                  <Card
                    key={product.id}
                    className={`cursor-pointer transition-all duration-150 hover:border-gray-300 ${
                      selectedProduct?.id === product.id
                        ? 'border-blue-400 bg-blue-50/50'
                        : 'hover:shadow-sm'
                    } ${product.alreadyLinked ? 'opacity-75' : ''}`}
                    onClick={() => handleProductSelect(product)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">

                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-base text-gray-900 mb-1 truncate">
                                {product.name}
                              </h3>
                              <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                                {product.description || "Sem descrição"}
                              </p>
                            </div>
                            <div className="flex items-center gap-1 ml-3 flex-shrink-0">
                              {product.alreadyLinked && (
                                <Badge status="warning" className="text-xs flex items-center bg-yellow-100 text-yellow-800 border-yellow-200">
                                  Já vinculado
                                </Badge>
                              )}
                              {selectedProduct?.id === product.id && (
                                <div className="bg-blue-500 text-white rounded-full p-1">
                                  <CheckIcon className="h-3 w-3" />
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="text-lg font-bold text-green-800">
                                {formatPrice(product.price)}
                              </div>
                              <div className="flex items-center gap-1">
                                <Badge
                                  status="success"
                                  className={`text-xs flex items-center font-medium ${
                                    product.status === 'active'
                                      ? 'border-green-400 text-green-900 bg-green-100'
                                      : 'border-gray-400 text-gray-900 bg-gray-100'
                                  }`}
                                >
                                  <ZapIcon className="h-3 w-3 mr-1" />
                                  {product.status === 'active' ? 'Ativo' : product.status}
                                </Badge>
                                <Badge status="info" className="text-xs flex items-center font-medium border-blue-400 text-blue-900 bg-blue-100">
                                  <TagIcon className="h-3 w-3 mr-1" />
                                  {product.type === 'unique' ? 'Produto Único' : product.type}
                                </Badge>
                              </div>
                            </div>
                          </div>

                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between pt-4 border-t bg-gray-50 flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="text-xs text-gray-700">
              <span className="font-medium">{filteredProducts.length}</span> produto{filteredProducts.length !== 1 ? 's' : ''} encontrado{filteredProducts.length !== 1 ? 's' : ''}
            </div>
            {selectedProduct && (
              <div className="flex items-center gap-1 text-xs text-blue-700">
                <CheckIcon className="h-3 w-3" />
                <span>Selecionado: <span className="font-medium">{selectedProduct.name}</span></span>
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel} className="px-4 h-8 text-sm">
              Cancelar
            </Button>
            {onProductSelect ? (
              <Button
                onClick={handleConfirmSelection}
                disabled={!selectedProduct}
                className="px-4 h-8 text-sm bg-blue-600 hover:bg-blue-700"
              >
                <CheckIcon className="h-3 w-3 mr-1" />
                Selecionar
              </Button>
            ) : (
              <Button
                onClick={handleCreateWithProduct}
                disabled={!selectedProduct}
                className="px-4 h-8 text-sm bg-green-600 hover:bg-green-700"
              >
                <PlusIcon className="h-3 w-3 mr-1" />
                Criar Curso
              </Button>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}
