'use client'

import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/modules/ui/components/dialog'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Label } from '@/modules/ui/components/label'
import { Badge } from '@/modules/ui/components/badge'
import { Card, CardContent } from '@/modules/ui/components/card'
import { Checkbox } from '@/modules/ui/components/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/modules/ui/components/select'
import { toast } from 'sonner'
import { Loader2Icon, SearchIcon, PackageIcon } from 'lucide-react'
import { useCaktoProducts } from '../../hooks/useCaktoProducts'

interface Organization {
  id: string
  name: string
  slug: string
}

interface ImportCaktoProductModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  organizations: Organization[]
}

export function ImportCaktoProductModal({ open, onOpenChange, organizations }: ImportCaktoProductModalProps) {
  const [selectedOrgId, setSelectedOrgId] = useState('')
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isImporting, setIsImporting] = useState(false)

  const { products, isLoading, error } = useCaktoProducts({
    organizationId: selectedOrgId || undefined,
    type: 'course'
  })

  const filteredProducts = products.filter((product: any) =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.description?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  )

  const handleProductToggle = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }

  const handleImport = async () => {
    if (!selectedOrgId) {
      toast.error('Selecione uma organização')
      return
    }

    if (selectedProducts.length === 0) {
      toast.error('Selecione pelo menos um produto')
      return
    }

    setIsImporting(true)

    try {
      // Mock import process
      await new Promise(resolve => setTimeout(resolve, 2000))

      toast.success(`${selectedProducts.length} produto(s) importado(s) com sucesso!`)
      onOpenChange(false)
      setSelectedProducts([])
      setSelectedOrgId('')
      setSearchTerm('')
    } catch (error) {
      toast.error('Erro ao importar produtos')
    } finally {
      setIsImporting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Importar Produtos da Cakto</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 flex-1 overflow-hidden flex flex-col">
          {/* Filters */}
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="organization">Organização de Destino</Label>
              <Select value={selectedOrgId} onValueChange={setSelectedOrgId}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma organização" />
                </SelectTrigger>
                <SelectContent>
                  {organizations.map((org) => (
                    <SelectItem key={org.id} value={org.id}>
                      {org.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1">
              <Label htmlFor="search">Buscar Produtos</Label>
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Digite o nome do produto..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Products List */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2Icon className="h-8 w-8 animate-spin text-gray-400" />
                <span className="ml-2 text-gray-600">Carregando produtos...</span>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <PackageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">{error}</p>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-8">
                <PackageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">
                  {searchTerm ? 'Nenhum produto encontrado' : 'Nenhum produto disponível'}
                </p>
              </div>
            ) : (
              <div className="grid gap-3">
                {filteredProducts.map((product: any) => (
                  <Card key={product.id} className="cursor-pointer hover:bg-gray-50 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <Checkbox
                          checked={selectedProducts.includes(product.id)}
                          onCheckedChange={() => handleProductToggle(product.id)}
                        />

                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900 truncate">{product.name}</h4>
                              <p className="text-sm text-gray-600 mt-1 line-clamp-2">{product.description || "Sem descrição"}</p>
                            </div>

                            {product.image && (
                              <img
                                src={product.image}
                                alt={product.name}
                                className="w-16 h-16 object-cover rounded-lg ml-4 flex-shrink-0"
                              />
                            )}
                          </div>

                          <div className="flex items-center gap-2 mt-3">
                            <Badge className="bg-green-100 text-green-800">
                              R$ {product.price.toFixed(2)}
                            </Badge>
                            <Badge status="info">
                              {product.type === 'course' ? 'Curso' : product.type}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-gray-600">
              {selectedProducts.length} produto(s) selecionado(s)
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isImporting}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleImport}
                disabled={isImporting || selectedProducts.length === 0 || !selectedOrgId}
              >
                {isImporting ? (
                  <>
                    <Loader2Icon className="h-4 w-4 mr-2 animate-spin" />
                    Importando...
                  </>
                ) : (
                  `Importar ${selectedProducts.length} produto(s)`
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
