import { apiClient } from "@shared/lib/api-client";
import { createQueryKeyWithParams } from "@shared/lib/query-client";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

/*
 * Admin users
 */
type FetchAdminUsersParams = {
	itemsPerPage: number;
	currentPage: number;
	searchTerm: string;
};

export const adminUsersQueryKey = ["admin", "users"];
export const fetchAdminUsers = async ({
	itemsPerPage,
	currentPage,
	searchTerm,
}: FetchAdminUsersParams) => {
	const response = await apiClient.admin.users.$get({
		query: {
			limit: itemsPerPage.toString(),
			offset: ((currentPage - 1) * itemsPerPage).toString(),
			query: searchTerm,
		},
	});

	if (!response.ok) {
		throw new Error("Could not fetch users");
	}

	return response.json();
};
export const useAdminUsersQuery = (params: FetchAdminUsersParams) =>
	useQuery({
		queryKey: [...adminUsersQueryKey, params],
		queryFn: () => fetchAdminUsers(params),
		placeholderData: keepPreviousData,
	});

/*
 * Admin organizations
 */
type FetchAdminOrganizationsParams = {
	itemsPerPage: number;
	currentPage: number;
	searchTerm: string;
};

export const adminOrganizationsQueryKey = ["admin", "organizations"];
export const fetchAdminOrganizations = async ({
	itemsPerPage,
	currentPage,
	searchTerm,
}: FetchAdminOrganizationsParams) => {
	const response = await apiClient.admin.organizations.$get({
		query: {
			limit: itemsPerPage.toString(),
			offset: ((currentPage - 1) * itemsPerPage).toString(),
			query: searchTerm,
		},
	});

	if (!response.ok) {
		throw new Error("Could not fetch organizations");
	}

	return response.json();
};
export const useAdminOrganizationsQuery = (
	params: FetchAdminOrganizationsParams,
) =>
	useQuery({
		queryKey: [...adminOrganizationsQueryKey, params],
		queryFn: () => fetchAdminOrganizations(params),
		placeholderData: keepPreviousData,
	});

/*
 * Admin vitrines
 */
type FetchAdminVitrinesParams = {
	itemsPerPage: number;
	currentPage: number;
	searchTerm: string;
};

export const adminVitrinesQueryKey = ["admin", "vitrines"];
export const fetchAdminVitrines = async ({
	itemsPerPage,
	currentPage,
	searchTerm,
}: FetchAdminVitrinesParams) => {
	const response = await apiClient.admin.vitrines.$get({
		query: {
			limit: itemsPerPage.toString(),
			offset: ((currentPage - 1) * itemsPerPage).toString(),
			query: searchTerm,
		},
	});

	if (!response.ok) {
		throw new Error("Could not fetch vitrines");
	}

	return response.json();
};
export const useAdminVitrinesQuery = (params: FetchAdminVitrinesParams) =>
	useQuery({
		queryKey: [...adminVitrinesQueryKey, params],
		queryFn: () => fetchAdminVitrines(params),
		placeholderData: keepPreviousData,
	});

/*
 * Admin Cakto products
 */
export const adminCaktoProductsQueryKey = ["admin", "cakto-products"];
export const fetchAdminCaktoProducts = async () => {
	// Use type assertion since the endpoint exists but types haven't been regenerated
	const response = await (apiClient.admin as any)["cakto-products"].$get();

	if (!response.ok) {
		const errorData = await response.json();
		throw new Error(errorData.error || "Could not fetch Cakto products");
	}

	return response.json();
};

export const useAdminCaktoProductsQuery = (enabled: boolean = true) =>
	useQuery({
		queryKey: adminCaktoProductsQueryKey,
		queryFn: fetchAdminCaktoProducts,
		staleTime: 5 * 60 * 1000, // 5 minutes
		retry: 2,
		enabled,
	});
