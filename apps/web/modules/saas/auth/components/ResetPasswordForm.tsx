"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import { AlertTriangleIcon, ArrowLeftIcon, MailboxIcon } from "lucide-react";

import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useAuthErrorMessages } from "@saas/auth/hooks/errors-messages";
import { useSession } from "@saas/auth/hooks/use-session";
import { useRouter } from "@shared/hooks/router";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { PasswordInput } from "@ui/components/password-input";
import { Logo } from "@shared/components/Logo";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import * as z from "zod";

const formSchema = z.object({
	password: z.string().min(8),
});

type FormValues = z.infer<typeof formSchema>;

export function ResetPasswordForm() {
	const t = useTranslations();
	const { user } = useSession();
	const router = useRouter();
	const { getAuthErrorMessage } = useAuthErrorMessages();
	const searchParams = useSearchParams();
	const token = searchParams.get("token");

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			password: "",
		},
	});

	const onSubmit = form.handleSubmit(async ({ password }) => {
		try {
			const { error } = await authClient.resetPassword({
				token: token ?? undefined,
				newPassword: password,
			});

			if (error) {
				throw error;
			}

			if (user) {
				router.push(config.auth.redirectAfterSignIn);
			}
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		}
	});

	return (
		<div className="grid min-h-svh lg:grid-cols-2">
			{/* Left Column - Banner */}
			<div className="relative hidden lg:block overflow-hidden">
				<img
					src="/images/banner1.jpg"
					alt="Cakto Members"
					className="absolute inset-0 h-full w-full object-cover"
				/>
				<div className="absolute inset-0 bg-black/40" />
				<div className="absolute inset-0 opacity-20 bg-gradient-to-br from-primary/20 to-primary/40" />
			</div>

			{/* Right Column - Form */}
			<div className="flex flex-col gap-4 p-6 md:p-10">
				{/* Logo Section */}
				<div className="flex justify-center gap-2 md:justify-start">
					<Link href="/">
						<Logo withLabel={true} />
					</Link>
				</div>

				{/* Form Section */}
				<div className="flex flex-1 items-center justify-center">
					<div className="w-full max-w-sm">
						<div className="grid gap-6">
							<div className="grid gap-2 text-left">
								<h1 className="text-3xl font-bold">
									{t("auth.resetPassword.title")}
								</h1>
								<p className="text-muted-foreground">
									{t("auth.resetPassword.message")}
								</p>
							</div>

							{form.formState.isSubmitSuccessful ? (
								<Alert variant="success">
									<MailboxIcon />
									<AlertTitle>
										{t("auth.resetPassword.hints.success")}
									</AlertTitle>
								</Alert>
							) : (
								<div className="grid gap-4">
									<Form {...form}>
										<form
											className="space-y-4"
											onSubmit={onSubmit}
										>
											{form.formState.errors.root && (
												<Alert variant="error">
													<AlertTriangleIcon />
													<AlertTitle>
														{form.formState.errors.root.message}
													</AlertTitle>
												</Alert>
											)}

											<FormField
												control={form.control}
												name="password"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															{t("auth.resetPassword.newPassword")}
														</FormLabel>
														<FormControl>
															<PasswordInput
																autoComplete="new-password"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<Button
												className="w-full"
												loading={form.formState.isSubmitting}
											>
												{t("auth.resetPassword.submit")}
											</Button>
										</form>
									</Form>

									<div className="mt-6 text-center text-sm">
										<Link href="/auth/login" className="text-foreground/60 hover:text-foreground">
											<ArrowLeftIcon className="mr-1 inline size-4 align-middle" />
											{t("auth.resetPassword.backToSignin")}
										</Link>
									</div>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
