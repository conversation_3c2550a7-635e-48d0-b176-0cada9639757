"use client";

import { useState } from "react";
import { apiClient } from "@shared/lib/api-client";
import { cn } from "@ui/lib";
import Image from "next/image";

interface SecureImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallback?: string;
  priority?: boolean;
}

export function SecureImage({
  src,
  alt,
  width = 400,
  height = 300,
  className,
  fallback = "/images/placeholder.jpg",
  priority = false,
}: SecureImageProps) {
  const [imageSrc, setImageSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const getSignedUrl = async (filePath: string) => {
    try {
      const response = await apiClient.get(`/api/storage/signed-url?path=${encodeURIComponent(filePath)}`);
      return response.data.url;
    } catch (error) {
      console.error("Failed to get signed URL:", error);
      return null;
    }
  };

  const handleImageLoad = async () => {
    if (imageSrc.startsWith("http")) {
      setImageSrc(imageSrc);
      return;
    }

    const signedUrl = await getSignedUrl(imageSrc);
    if (signedUrl) {
      setImageSrc(signedUrl);
    } else {
      setHasError(true);
    }
  };

  const handleError = () => {
    setHasError(true);
    setImageSrc(fallback);
  };

  if (hasError) {
    return (
      <div
        className={cn(
          "flex items-center justify-center bg-muted text-muted-foreground",
          className
        )}
        style={{ width, height }}
      >
        <span className="text-sm">Imagem não disponível</span>
      </div>
    );
  }

  return (
    <Image
      src={imageSrc}
      alt={alt}
      width={width}
      height={height}
      className={cn(
        "object-cover transition-opacity duration-300",
        isLoading ? "opacity-0" : "opacity-100",
        className
      )}
      onLoad={() => setIsLoading(false)}
      onError={handleError}
      priority={priority}
    />
  );
}
