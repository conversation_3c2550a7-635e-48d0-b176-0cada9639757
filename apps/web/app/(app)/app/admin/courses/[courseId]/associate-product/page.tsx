"use client";

import { useEffect, useState, useMemo } from "react";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { ArrowLeftIcon, SearchIcon, LinkIcon, CheckCircleIcon, AlertCircleIcon, LoaderIcon } from "lucide-react";
import { toast } from "sonner";
import { useCaktoProducts } from '@/modules/saas/admin/hooks/useCaktoProducts'

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
  alreadyLinked: boolean;
}

interface Course {
  id: string;
  name: string;
  description?: string;
}

export default function AssociateProductPage() {
  const params = useParams();
  const courseId = params.courseId as string;
  const router = useRouter();
  const [course, setCourse] = useState<Course | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [associating, setAssociating] = useState<string | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);

  const { products, isLoading: productsLoading, error: productsError } = useCaktoProducts();

  // Filter products based on search query
  const filteredProducts = useMemo(() => {
    if (searchQuery.trim() === "") {
      return products;
    }

    return products.filter((product: any) =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery, products]);

  // Carregar token de acesso do localStorage
  useEffect(() => {
    const token = localStorage.getItem("accessToken");
    setAccessToken(token);
  }, []);

  // Carregar dados do curso
  useEffect(() => {
    const fetchCourse = async () => {
      try {
        setLoading(true);
        // Implementar chamada para buscar dados do curso
        // Por enquanto, usando dados mock
        setCourse({
          id: courseId,
          name: "Curso de Exemplo",
          description: "Descrição do curso"
        });
      } catch (err) {
        setError("Erro ao carregar dados do curso");
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [courseId]);

  // Associar produto ao curso
  const associateProduct = async (product: Product) => {
    if (product.alreadyLinked) {
      toast.error("Produto já associado");
      return;
    }

    setAssociating(product.id);

    try {
      const response = await fetch("/api/admin/cakto-products/associate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${accessToken}`
        },
        body: JSON.stringify({
          courseId,
          caktoProductId: product.id,
          caktoProductName: product.name
        })
      });

      if (!response.ok) {
        throw new Error("Erro ao associar produto ao curso");
      }

      const data = await response.json();

      toast.success(`O produto "${product.name}" foi associado ao curso "${course?.name}"`);

      // Refresh the products data to update the alreadyLinked status
      window.location.reload();
    } catch (err) {
      toast.error("Não foi possível associar o produto ao curso");
    } finally {
      setAssociating(null);
    }
  };

  // Show error if products failed to load
  if (productsError) {
    return (
      <div className="container py-6 space-y-6">
        <div className="flex items-center gap-2 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-1"
          >
            <ArrowLeftIcon className="h-4 w-4" />
            Voltar
          </Button>
          <h1 className="text-2xl font-bold">Associar Produto ao Curso</h1>
        </div>

        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircleIcon className="h-5 w-5 text-destructive" />
              Erro ao carregar produtos
            </CardTitle>
            <CardDescription>
              {productsError}
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => router.back()}>Voltar</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-1"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Voltar
        </Button>
        <h1 className="text-2xl font-bold">Associar Produto ao Curso</h1>
      </div>

      {course?.name && (
        <div className="text-lg mb-6">
          Curso: <span className="font-semibold">{course.name}</span>
        </div>
      )}

      {error ? (
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircleIcon className="h-5 w-5 text-destructive" />
              Erro
            </CardTitle>
            <CardDescription>
              {error}
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => router.back()}>Voltar</Button>
          </CardFooter>
        </Card>
      ) : (
        <>
          <div className="flex items-center gap-2 mb-4">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar produtos por nome ou descrição..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {productsLoading ? (
            <div className="flex justify-center items-center py-12">
              <LoaderIcon className="h-8 w-8 animate-spin text-primary mr-2" />
              <span>Carregando produtos...</span>
            </div>
          ) : filteredProducts.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <AlertCircleIcon className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-1">Nenhum produto encontrado</h3>
                <p className="text-muted-foreground text-center mb-4">
                  {searchQuery ? "Tente ajustar sua busca" : "Não há produtos disponíveis para associação"}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {filteredProducts.map((product: any) => (
                <Card key={product.id} className="overflow-hidden">
                  <div className="h-40 bg-muted relative">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary/5 to-primary/10">
                        <LinkIcon className="h-12 w-12 text-muted-foreground" />
                      </div>
                    )}
                    {product.alreadyLinked && (
                      <div className="absolute top-2 right-2">
                        <Badge status="success" className="flex items-center gap-1">
                          <CheckCircleIcon className="h-3 w-3" />
                          Associado
                        </Badge>
                      </div>
                    )}
                  </div>
                  <CardHeader>
                    <CardTitle className="line-clamp-1">{product.name}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {product.description || "Sem descrição"}
                    </CardDescription>
                  </CardHeader>
                  <CardFooter className="flex justify-between">
                    <div className="text-sm font-medium">
                      {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(product.price || 0)}
                    </div>
                    <Button
                      variant={product.alreadyLinked ? "outline" : "primary"}
                      size="sm"
                      disabled={associating === product.id}
                      onClick={() => associateProduct(product)}
                    >
                      {associating === product.id ? (
                        <>
                          <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                          Associando...
                        </>
                      ) : product.alreadyLinked ? (
                        <>
                          <CheckCircleIcon className="h-4 w-4 mr-2" />
                          Associado
                        </>
                      ) : (
                        <>
                          <LinkIcon className="h-4 w-4 mr-2" />
                          Associar
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
}
