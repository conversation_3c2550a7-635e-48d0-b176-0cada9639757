"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Progress } from "@ui/components/progress";
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	CheckIcon,
	SaveIcon,
	ArrowLeftIcon,
	PackageIcon,
} from "lucide-react";
import { toast } from "sonner";
import { AdminPageLayout } from "@saas/admin/component/shared/AdminPageLayout";
import { CourseBasicForm } from "./CourseBasicForm";
import { CourseStructureForm } from "./CourseStructureForm";
import { CourseSettingsForm } from "./CourseSettingsForm";
import { CoursePreview } from "./CoursePreview";
import type { CourseFormData } from "../types";
import { apiClient } from "@shared/lib/api-client";

const STEPS = [
	{
		id: "basic",
		title: "Informações Básicas",
		description: "Nome, descrição e organização",
	},
	{
		id: "structure",
		title: "Estrutura e Conteúdo",
		description: "Módulos, aulas e upload de materiais",
	},
	{
		id: "settings",
		title: "Configurações",
		description: "Configurações de acesso e publicação",
	},
	{
		id: "preview",
		title: "Visualizar e Publicar",
		description: "Revise e publique seu curso",
	},
];

interface CourseFormProps {
	mode: "create" | "edit";
	courseId?: string;
}

export default function CourseForm({ mode, courseId }: CourseFormProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [currentStep, setCurrentStep] = useState(0);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingCourse, setIsLoadingCourse] = useState(mode === "edit");

	// Get Cakto product info from URL params
	const caktoProductId = searchParams.get('caktoProductId');
	const caktoProductName = searchParams.get('caktoProductName');

	const [formData, setFormData] = useState<CourseFormData>({
		name: "",
		description: "",
		organizationId: "",
		community: "",
		link: "",
		logo: "",
		modules: [],
	});

	// Load course data when in edit mode
	useEffect(() => {
		if (mode === "edit" && courseId) {
			loadCourseData();
		}
	}, [mode, courseId]);

	const loadCourseData = async () => {
		try {
			setIsLoadingCourse(true);

			// Fetch course data from admin API
			const response = await fetch(`/api/courses/admin/${courseId}`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
			});

			if (!response.ok) {
				const errorData = await response.json() as any;
				throw new Error(errorData.error || "Failed to fetch course");
			}

			const courseData = await response.json() as any;

			// Transform API data to form data structure
			setFormData({
				name: courseData.name || "",
				description: courseData.description || "",
				organizationId: courseData.organizationId || "",
				community: courseData.community || "",
				link: courseData.link || "",
				logo: courseData.logo || "",
				modules: (courseData.modules || []).map((module: any) => ({
					...module,
					cover: module.cover || undefined,
					lessons: (module.lessons || []).map((lesson: any) => ({
						...lesson,
						description: lesson.description || undefined,
						duration: lesson.duration || undefined,
						videoUrl: lesson.videoUrl || undefined,
						thumbnail: lesson.thumbnail || undefined,
					})),
				})),
			});
		} catch (error) {
			console.error("Error loading course:", error);
			toast.error("Erro ao carregar dados do curso");
			router.push("/app/admin/courses");
		} finally {
			setIsLoadingCourse(false);
		}
	};

	const updateFormData = useCallback((updates: Partial<CourseFormData>) => {
		setFormData((prev: CourseFormData) => ({ ...prev, ...updates }));
	}, []);

	const handleNext = useCallback(() => {
		if (currentStep < STEPS.length - 1) {
			setCurrentStep((prev) => prev + 1);
		}
	}, [currentStep]);

	const handlePrevious = useCallback(() => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		}
	}, [currentStep]);

	const handleSave = useCallback(async () => {
		setIsLoading(true);
		try {
			// Transform modules to match API schema
			const transformedModules = formData.modules.map((module: any, index: number) => ({
				name: module.name,
				position: module.position || index,
				cover: module.cover,
			}));

			const payload = {
				name: formData.name,
				description: formData.description,
				organizationId: formData.organizationId,
				community: formData.community,
				link: formData.link,
				logo: formData.logo,
				modules: transformedModules,
			};

			let response;
			if (mode === "create") {
				// Create new course
				response = await apiClient.courses.$post({
					json: payload,
				});
			} else {
				// Update existing course
				response = await fetch(`/api/courses/${courseId}`, {
					method: 'PUT',
					headers: {
						'Content-Type': 'application/json',
					},
					credentials: 'include',
					body: JSON.stringify(payload),
				});
			}

			const responseData = await response.json();

			if (!response.ok) {
				throw new Error((responseData as any).error || `Failed to ${mode} course`);
			}

			// If we have a Cakto product ID and this is a new course, associate the product
			if (mode === "create" && caktoProductId && responseData.id) {
				try {
					const associateResponse = await apiClient.admin["cakto-products"].associate.$post({
						json: {
							courseId: responseData.id,
							caktoProductId: caktoProductId,
							caktoProductName: caktoProductName || ''
						}
					});

					if (associateResponse.ok) {
						toast.success("Curso criado e produto da Cakto associado com sucesso!");
					} else {
						toast.warning("Curso criado, mas houve um problema ao associar o produto da Cakto");
					}
				} catch (associateError) {
					console.error('Error associating Cakto product:', associateError);
					toast.warning("Curso criado, mas houve um problema ao associar o produto da Cakto");
				}
			} else {
				toast.success(mode === "create" ? "Curso criado com sucesso!" : "Curso atualizado com sucesso!");
			}

			router.push("/app/admin/courses");
		} catch (error) {
			toast.error(error instanceof Error ? error.message : `Erro ao ${mode === "create" ? "criar" : "atualizar"} curso`);
			console.error(`Error ${mode}ing course:`, error);
		} finally {
			setIsLoading(false);
		}
	}, [formData, router, mode, courseId]);

	const isStepValid = useCallback((stepIndex: number) => {
		switch (stepIndex) {
			case 0: // Basic info
				return formData.name && formData.organizationId;
			case 1: // Structure and Content
				return formData.modules.length > 0;
			case 2: // Settings
				return true;
			case 3: // Preview
				return true;
			default:
				return false;
		}
	}, [formData]);

	const canProceed = isStepValid(currentStep);
	const progress = ((currentStep + 1) / STEPS.length) * 100;

	// Render Cakto product info if available
	const renderCaktoProductInfo = () => {
		if (!caktoProductId || !caktoProductName) return null;

		return (
			<Card className="mb-6 border-primary/20 bg-primary/5">
				<CardContent className="pt-6">
					<div className="flex items-center gap-3">
						<PackageIcon className="h-5 w-5 text-primary" />
						<div className="flex-1">
							<h3 className="font-semibold text-sm">Produto da Cakto Associado</h3>
							<p className="text-sm text-muted-foreground">{caktoProductName}</p>
						</div>
						<Badge className="text-xs">
							ID: {caktoProductId}
						</Badge>
					</div>
				</CardContent>
			</Card>
		);
	};

	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return (
					<CourseBasicForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
					/>
				);
			case 1:
				return (
					<CourseStructureForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
						onPrevious={handlePrevious}
					/>
				);
			case 2:
				return (
					<CourseSettingsForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
						onPrevious={handlePrevious}
					/>
				);
			case 3:
				return (
					<CoursePreview
						data={formData}
						onPrevious={handlePrevious}
					/>
				);
			default:
				return null;
		}
	};

	if (isLoadingCourse) {
		return (
			<AdminPageLayout
				title="Carregando..."
				subtitle="Aguarde enquanto carregamos os dados do curso"
			>
				<Card>
					<CardContent className="py-12">
						<div className="flex items-center justify-center">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
							<span className="ml-2 text-muted-foreground">Carregando curso...</span>
						</div>
					</CardContent>
				</Card>
			</AdminPageLayout>
		);
	}

	const pageTitle = mode === "create" ? "Criar Novo Curso" : "Editar Curso";
	const pageSubtitle = mode === "create" ? "Configure seu curso em etapas simples" : "Atualize as informações do seu curso";

	return (
		<AdminPageLayout
			title={pageTitle}
			subtitle={pageSubtitle}
			actionButton={mode === "edit" ? {
				label: "Voltar",
				onClick: () => router.push("/app/admin/courses"),
				icon: <ArrowLeftIcon className="mr-2 h-4 w-4" />,
			} : undefined}
		>
			{/* Cakto Product Info */}
			{renderCaktoProductInfo()}

			{/* Progress Header */}
			<Card className="mb-8">
				<CardHeader className="pb-4">
					<div className="flex items-center justify-between mb-4">
						<div>
							<CardTitle className="text-lg">
								Etapa {currentStep + 1} de {STEPS.length}
							</CardTitle>
							<p className="text-muted-foreground text-sm mt-1">
								{STEPS[currentStep].description}
							</p>
						</div>
						<Badge status="info">
							{Math.round(progress)}% concluído
						</Badge>
					</div>
					<Progress value={progress} className="h-2" />
				</CardHeader>
				<CardContent className="pt-0">
					<div className="flex items-center justify-between">
						{STEPS.map((step, index) => (
							<div
								key={step.id}
								className="flex items-center gap-3"
							>
								<div
									className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
										index < currentStep
											? "bg-green-500 border-green-500 text-white"
											: index === currentStep
											? "bg-primary border-primary text-white"
											: "bg-background border-border text-muted-foreground"
									}`}
								>
									{index < currentStep ? (
										<CheckIcon className="h-4 w-4" />
									) : (
										<span className="text-sm font-medium">
											{index + 1}
										</span>
									)}
								</div>
								<div className="hidden sm:block">
									<p
										className={`font-medium text-sm ${
											index <= currentStep
												? "text-foreground"
												: "text-muted-foreground"
										}`}
									>
										{step.title}
									</p>
								</div>
								{index < STEPS.length - 1 && (
									<div
										className={`hidden sm:block w-12 h-px mx-4 ${
											index < currentStep
												? "bg-green-500"
												: "bg-border"
										}`}
									/>
								)}
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Step Content */}
			<div className="min-h-[600px]">
				{renderStepContent()}
			</div>

			{/* Navigation Footer */}
			<Card className="mt-8">
				<CardContent className="py-6">
					<div className="flex items-center justify-between">
						<div className="flex gap-2">
							{currentStep > 0 && (
								<Button
									variant="outline"
									onClick={handlePrevious}
									disabled={isLoading}
								>
									<ChevronLeftIcon className="mr-2 h-4 w-4" />
									Anterior
								</Button>
							)}
						</div>

						<div className="flex gap-2">
							{mode === "edit" && (
								<Button
									variant="outline"
									onClick={handleSave}
									disabled={isLoading || !formData.name}
								>
									<SaveIcon className="mr-2 h-4 w-4" />
									Salvar Alterações
								</Button>
							)}

							{currentStep < STEPS.length - 1 ? (
								<Button
									onClick={handleNext}
									disabled={!canProceed || isLoading}
								>
									Próximo
									<ChevronRightIcon className="ml-2 h-4 w-4" />
								</Button>
							) : (
								<div className="flex gap-2">
									{mode === "create" && (
										<Button
											variant="outline"
											onClick={handleSave}
											disabled={isLoading || !formData.name}
										>
											<SaveIcon className="mr-2 h-4 w-4" />
											Salvar Rascunho
										</Button>
									)}
									<Button
										onClick={handleSave}
										disabled={!canProceed || isLoading}
										className="bg-green-600 hover:bg-green-700"
									>
										<CheckIcon className="mr-2 h-4 w-4" />
										{mode === "create" ? "Criar Curso" : "Finalizar Edição"}
									</Button>
								</div>
							)}
						</div>
					</div>
				</CardContent>
			</Card>
		</AdminPageLayout>
	);
}
