"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent } from "@ui/components/card";
import { LoaderIcon } from "lucide-react";
import { authClient } from "@repo/auth/client";

export default function SSOCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processAuthorizationCode = async () => {
      try {
        const code = searchParams.get("code");
        const errorParam = searchParams.get("error");

        if (errorParam) {
          setError(`Erro de autorização: ${errorParam}`);
          return;
        }

        if (!code) {
          setError("Código de autorização não encontrado na URL");
          return;
        }

        const caktoApiUrl = process.env.NEXT_PUBLIC_CAKTO_API_URL || "https://api.cakto.com.br";
        const clientId = process.env.NEXT_PUBLIC_CAKTO_CLIENT_ID || "members-area";
        const clientSecret = process.env.CAKTO_CLIENT_SECRET;
        const redirectUri = new URL("/auth/sso/callback", window.location.origin).toString();

        const tokenResponse = await fetch(`${caktoApiUrl}/oauth/token/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: new URLSearchParams({
            grant_type: "authorization_code",
            code: code,
            client_id: clientId,
            client_secret: clientSecret || "",
            redirect_uri: redirectUri,
          }),
        });

        if (!tokenResponse.ok) {
          const errorData = await tokenResponse.text();
          console.error("Erro ao trocar código por token:", errorData);
          throw new Error("Falha na troca do código de autorização por token");
        }

        const tokenData = await tokenResponse.json();
        const accessToken = tokenData.access_token;

        if (!accessToken) {
          throw new Error("Token de acesso não recebido");
        }

        const userInfoResponse = await fetch(`${caktoApiUrl}/oauth/userinfo/`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });

        if (!userInfoResponse.ok) {
          throw new Error("Falha na obtenção das informações do usuário");
        }

        const userInfo = await userInfoResponse.json();

        try {
          const { error: signInError } = await authClient.signIn.email({
            email: userInfo.email,
            password: "",
          });

          if (signInError) {
            const { error: signUpError } = await authClient.signUp.email({
              email: userInfo.email,
              name: userInfo.name || userInfo.email,
              password: Math.random().toString(36).slice(-12),
            });

            if (signUpError) {
              throw new Error("Falha ao criar conta do usuário");
            }
          }
        } catch (authError) {
          console.error("Erro na autenticação:", authError);
        }

        const redirectPath = localStorage.getItem("sso_redirect_path") || "/app";
        localStorage.removeItem("sso_redirect_path");

        const finalRedirectPath = new URL(redirectPath, window.location.origin);
        if (searchParams.get("invitationId")) {
          finalRedirectPath.searchParams.set("invitationId", searchParams.get("invitationId")!);
        }
        if (searchParams.get("email")) {
          finalRedirectPath.searchParams.set("email", searchParams.get("email")!);
        }

        router.push(finalRedirectPath.toString());
      } catch (err) {
        console.error("Erro ao processar código SSO:", err);
        setError("Erro ao processar autenticação. Por favor, tente novamente.");
      }
    };

    processAuthorizationCode();
  }, [router, searchParams]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center p-6">
          {error ? (
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Erro de Autenticação</h2>
              <p className="text-muted-foreground mb-4">{error}</p>
              <button
                onClick={() => router.push("/auth/login")}
                className="text-primary hover:underline"
              >
                Voltar para a página de login
              </button>
            </div>
          ) : (
            <div className="text-center">
              <LoaderIcon className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
              <h2 className="text-lg font-semibold mb-2">Processando autenticação</h2>
              <p className="text-muted-foreground">Aguarde enquanto concluímos o processo de login...</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
