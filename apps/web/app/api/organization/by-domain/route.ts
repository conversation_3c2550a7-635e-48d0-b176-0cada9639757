import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const host = searchParams.get('host');

    if (!host) {
      return NextResponse.json({ error: 'Host parameter is required' }, { status: 400 });
    }

    // Check for custom domain first
    let organization = await db.organization.findFirst({
      where: { customDomain: host },
      include: { members: true, invitations: true },
    });

    if (organization) {
      return NextResponse.json(organization);
    }

    // Check for subdomain
    const subdomain = extractSubdomain(host);
    if (subdomain) {
      organization = await db.organization.findFirst({
        where: { subdomain },
        include: { members: true, invitations: true },
      });
    }

    return NextResponse.json(organization);
  } catch (error) {
    console.error("Error fetching organization by domain:", error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function extractSubdomain(host: string): string | null {
  const parts = host.split('.');

  // For cakto.com.br subdomains
  if (parts.length >= 4 && parts.slice(-3).join('.') === 'cakto.com.br') {
    return parts[0];
  }

  return null;
}
