#!/usr/bin/env tsx

import { db } from "@repo/database";
import { auth } from "../packages/auth";

async function createTestUser() {
  console.log("👤 Criando usuário de teste para login...\n");

  const testEmail = "<EMAIL>";
  const testPassword = "TestPassword123!";
  const testName = "Usuário Teste";

  try {
    // 1. Verificar se usuário já existe
    const existingUser = await db.user.findUnique({
      where: { email: testEmail },
      include: {
        accounts: true
      }
    });

    let userId: string;

    if (existingUser) {
      console.log(`⚠️  Usuário ${testEmail} já existe.`);
      userId = existingUser.id;

      // Atualizar dados do usuário
      await db.user.update({
        where: { email: testEmail },
        data: {
          emailVerified: true
        }
      });

      console.log("✅ Dados do usuário atualizados!");
    } else {
      console.log(`🆕 Criando novo usuário ${testEmail}...`);

      // Criar novo usuário
      const newUser = await db.user.create({
        data: {
          email: testEmail,
          name: testName,
          emailVerified: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      userId = newUser.id;
      console.log("✅ Novo usuário criado com sucesso!");
    }

    // 2. Criar/atualizar conta com senha usando Better Auth
    console.log("🔐 Configurando senha com Better Auth...");

    // Usar Better Auth para hash da senha
    const authContext = await auth.$context;
    const hashedPassword = await authContext.password.hash(testPassword);

    // Verificar se já existe uma conta credential
    const existingAccount = await db.account.findUnique({
      where: {
        userId_providerId: {
          userId: userId,
          providerId: "credential"
        }
      }
    });

    if (existingAccount) {
      // Atualizar conta existente
      await db.account.update({
        where: {
          userId_providerId: {
            userId: userId,
            providerId: "credential"
          }
        },
        data: {
          password: hashedPassword,
          updatedAt: new Date()
        }
      });
      console.log("✅ Senha da conta existente atualizada!");
    } else {
      // Criar nova conta
      await db.account.create({
        data: {
          userId: userId,
          providerId: "credential",
          accountId: testEmail,
          password: hashedPassword,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      console.log("✅ Nova conta com senha criada!");
    }

    // 3. Verificar se usuário e conta foram criados corretamente
    const user = await db.user.findUnique({
      where: { email: testEmail },
      include: {
        accounts: {
          where: {
            providerId: "credential"
          }
        }
      }
    });

    if (user) {
      console.log("\n📋 Detalhes do usuário:");
      console.log(`   - ID: ${user.id}`);
      console.log(`   - Email: ${user.email}`);
      console.log(`   - Nome: ${user.name}`);
      console.log(`   - Email Verificado: ${user.emailVerified ? "✅" : "❌"}`);
      console.log(`   - Criado em: ${user.createdAt}`);

      const credentialAccount = user.accounts[0];
      if (credentialAccount) {
        console.log(`   - Conta Credential: ✅ Existe`);
        console.log(`   - Senha na Conta: ${credentialAccount.password ? "✅ Definida" : "❌ Não definida"}`);
      } else {
        console.log(`   - Conta Credential: ❌ Não encontrada`);
      }
    }

    // 4. Testar verificação de senha usando Better Auth
    if (user?.accounts[0]?.password) {
      console.log("\n🔐 Testando verificação de senha com Better Auth...");
      try {
        const isPasswordValid = await authContext.password.verify(testPassword, user.accounts[0].password);
        console.log(`   - Senha válida: ${isPasswordValid ? "✅" : "❌"}`);
      } catch (error) {
        console.log(`   - Erro na verificação: ${error}`);
      }
    }

    console.log("\n🎯 Credenciais para teste:");
    console.log(`   - Email: ${testEmail}`);
    console.log(`   - Senha: ${testPassword}`);
    console.log("\n✅ Usuário de teste pronto para login!");

  } catch (error) {
    console.error("❌ Erro ao criar usuário de teste:", error);
    process.exit(1);
  }
}

// Executar
createTestUser()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Erro:", error);
    process.exit(1);
  });
