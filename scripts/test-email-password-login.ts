#!/usr/bin/env tsx

import { auth } from "../packages/auth";
import { db } from "@repo/database";
import { getBaseUrl } from "@repo/utils";

async function testEmailPasswordLogin() {
  console.log("🔍 Testando login com email e senha...\n");

  const baseUrl = getBaseUrl();
  console.log(`🌐 Base URL: ${baseUrl}`);

  // 1. Verificar configuração do Better Auth
  console.log("\n1. 📋 Configuração Better Auth:");
  console.log(`   - Email/Password habilitado: ${auth.options.emailAndPassword?.enabled ? "✅" : "❌"}`);
  console.log(`   - Base URL: ${auth.options.baseURL}`);
  console.log(`   - Database configurado: ${auth.options.database ? "✅" : "❌"}`);

  // 2. Verificar se há usuários no banco
  console.log("\n2. 🗄️ Verificando usuários no banco:");
  try {
    const userCount = await db.user.count();
    console.log(`   - Total de usuários: ${userCount}`);

    if (userCount > 0) {
      const usersWithPassword = await db.user.count({
        where: {
          password: {
            not: null
          }
        }
      });
      console.log(`   - Usuários com senha: ${usersWithPassword}`);

      // Mostrar alguns usuários (sem senhas)
      const sampleUsers = await db.user.findMany({
        take: 3,
        select: {
          id: true,
          email: true,
          name: true,
          password: true,
          emailVerified: true,
          createdAt: true
        }
      });

      console.log("   - Usuários de exemplo:");
      sampleUsers.forEach(user => {
        console.log(`     • ${user.email} (${user.name}) - Senha: ${user.password ? "✅ Definida" : "❌ Não definida"} - Verificado: ${user.emailVerified ? "✅" : "❌"}`);
      });
    }
  } catch (error) {
    console.log(`   ❌ Erro ao acessar banco: ${error}`);
  }

  // 3. Testar criação de usuário de teste
  console.log("\n3. 👤 Criando usuário de teste...");
  const testEmail = "<EMAIL>";
  const testPassword = "TestPassword123!";

  try {
    // Verificar se usuário já existe
    const existingUser = await db.user.findUnique({
      where: { email: testEmail }
    });

    if (existingUser) {
      console.log(`   ⚠️  Usuário ${testEmail} já existe`);
      
      // Atualizar senha se necessário
      if (!existingUser.password) {
        console.log("   🔧 Atualizando senha do usuário existente...");
        
        // Usar Better Auth para hash da senha
        const hashedPassword = await auth.api.hashPassword({
          body: { password: testPassword }
        });

        if (hashedPassword.data) {
          await db.user.update({
            where: { email: testEmail },
            data: { 
              password: hashedPassword.data.hash,
              emailVerified: true
            }
          });
          console.log("   ✅ Senha atualizada");
        }
      }
    } else {
      console.log(`   🆕 Criando novo usuário ${testEmail}...`);
      
      // Usar Better Auth para criar usuário
      const result = await auth.api.signUpEmail({
        body: {
          email: testEmail,
          password: testPassword,
          name: "Usuário Teste"
        }
      });

      if (result.data) {
        console.log("   ✅ Usuário criado com sucesso");
      } else {
        console.log(`   ❌ Erro ao criar usuário: ${result.error}`);
      }
    }
  } catch (error) {
    console.log(`   ❌ Erro: ${error}`);
  }

  // 4. Testar login via API
  console.log("\n4. 🔐 Testando login via API...");
  try {
    const loginResult = await auth.api.signInEmail({
      body: {
        email: testEmail,
        password: testPassword
      }
    });

    if (loginResult.data) {
      console.log("   ✅ Login bem-sucedido via API!");
      console.log(`   - User ID: ${loginResult.data.user.id}`);
      console.log(`   - Email: ${loginResult.data.user.email}`);
      console.log(`   - Session: ${loginResult.data.session ? "✅ Criada" : "❌ Não criada"}`);
    } else {
      console.log(`   ❌ Erro no login: ${loginResult.error?.message || "Erro desconhecido"}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro no teste de login: ${error}`);
  }

  // 5. Testar endpoint HTTP
  console.log("\n5. 🌐 Testando endpoint HTTP...");
  try {
    const response = await fetch(`${baseUrl}/api/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword
      })
    });

    console.log(`   - Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log("   ✅ Login HTTP bem-sucedido!");
      console.log(`   - Resposta: ${JSON.stringify(data, null, 2)}`);
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Erro HTTP: ${errorText}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro na requisição HTTP: ${error}`);
  }

  // 6. Verificar configurações específicas
  console.log("\n6. ⚙️ Verificações Adicionais:");
  console.log(`   - NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`   - BETTER_AUTH_SECRET: ${process.env.BETTER_AUTH_SECRET ? "✅ Definido" : "❌ Faltando"}`);
  console.log(`   - DATABASE_URL: ${process.env.DATABASE_URL ? "✅ Definido" : "❌ Faltando"}`);

  console.log("\n✅ Teste de login com email/senha concluído!");
}

// Executar teste
testEmailPasswordLogin()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Erro no teste:", error);
    process.exit(1);
  });
