#!/usr/bin/env tsx

import { getBaseUrl } from "@repo/utils";

async function testProductionEndpoints() {
  console.log("🧪 Testando endpoints de autenticação em produção...\n");

  const baseUrl = getBaseUrl();
  console.log(`🌐 Base URL: ${baseUrl}\n`);

  // Função para fazer requisições HTTP
  async function testEndpoint(name: string, url: string, options: RequestInit = {}) {
    try {
      console.log(`🔍 Testando ${name}:`);
      console.log(`   URL: ${url}`);
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Members-Base-Test/1.0',
          ...options.headers,
        },
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.status === 200 || response.status === 302 || response.status === 401) {
        console.log(`   ✅ Endpoint respondendo corretamente`);
      } else {
        console.log(`   ⚠️  Status inesperado: ${response.status}`);
      }

      // Tentar ler o corpo da resposta
      try {
        const contentType = response.headers.get('content-type');
        if (contentType?.includes('application/json')) {
          const data = await response.json();
          console.log(`   Resposta: ${JSON.stringify(data, null, 2)}`);
        } else {
          const text = await response.text();
          if (text.length < 200) {
            console.log(`   Resposta: ${text}`);
          } else {
            console.log(`   Resposta: ${text.substring(0, 200)}...`);
          }
        }
      } catch (e) {
        console.log(`   (Não foi possível ler o corpo da resposta)`);
      }

      console.log('');
      return response;
    } catch (error) {
      console.log(`   ❌ Erro: ${error}`);
      console.log('');
      return null;
    }
  }

  // 1. Testar endpoint de sessão
  await testEndpoint(
    "Sessão Atual",
    `${baseUrl}/api/auth/session`,
    { method: 'GET' }
  );

  // 2. Testar endpoint de login com email (deve retornar 400 ou 401 sem credenciais)
  await testEndpoint(
    "Login com Email (sem credenciais)",
    `${baseUrl}/api/auth/sign-in/email`,
    { 
      method: 'POST',
      body: JSON.stringify({})
    }
  );

  // 3. Testar endpoint de magic link (deve retornar 400 sem email)
  await testEndpoint(
    "Magic Link (sem email)",
    `${baseUrl}/api/auth/sign-in/magic-link`,
    { 
      method: 'POST',
      body: JSON.stringify({})
    }
  );

  // 4. Testar endpoint de SSO
  await testEndpoint(
    "SSO Django",
    `${baseUrl}/api/auth/oauth2/django-sso`,
    { method: 'GET' }
  );

  // 5. Testar endpoint de verificação de magic link (deve retornar 400 sem token)
  await testEndpoint(
    "Magic Link Verify (sem token)",
    `${baseUrl}/api/auth/magic-link/verify`,
    { method: 'GET' }
  );

  // 6. Testar com credenciais de teste
  console.log("🔐 Testando com credenciais de exemplo...\n");

  await testEndpoint(
    "Login com Email (credenciais de teste)",
    `${baseUrl}/api/auth/sign-in/email`,
    { 
      method: 'POST',
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "wrongpassword"
      })
    }
  );

  await testEndpoint(
    "Magic Link (email de teste)",
    `${baseUrl}/api/auth/sign-in/magic-link`,
    { 
      method: 'POST',
      body: JSON.stringify({
        email: "<EMAIL>",
        callbackURL: "/app"
      })
    }
  );

  console.log("📋 Resumo dos Testes:");
  console.log("✅ Se os endpoints estão respondendo (não retornando erro de conexão)");
  console.log("✅ Se as URLs estão corretas (não apontando para localhost)");
  console.log("✅ Se o Better Auth está processando as requisições");
  console.log("");
  console.log("🎯 Próximos Passos:");
  console.log("1. Fazer deploy com as correções aplicadas");
  console.log("2. Testar login real no navegador");
  console.log("3. Verificar logs do container para erros específicos");
  console.log("4. Testar magic link com email real");
  console.log("5. Testar SSO com credenciais do Cakto");
}

// Executar testes
testProductionEndpoints()
  .then(() => {
    console.log("✅ Testes de endpoints concluídos!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Erro nos testes:", error);
    process.exit(1);
  });
