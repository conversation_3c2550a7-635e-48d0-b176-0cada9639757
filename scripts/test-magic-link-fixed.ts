#!/usr/bin/env node

import { config } from "dotenv";
import { resolve } from "path";

// Carregar variáveis de ambiente do .env.local
config({ path: resolve(__dirname, "../.env.local") });

import { auth } from "../packages/auth";
import { getBaseUrl } from "../packages/utils";

async function testMagicLink() {
	console.log("🧪 Testando Magic Link...");
	console.log("📋 Configurações:");
	console.log(`   - Base URL: ${getBaseUrl()}`);
	console.log(`   - NODE_ENV: ${process.env.NODE_ENV}`);
	console.log(`   - BETTER_AUTH_SECRET: ${process.env.BETTER_AUTH_SECRET ? "✅ Configurado" : "❌ Não configurado"}`);
	console.log(`   - DATABASE_URL: ${process.env.DATABASE_URL ? "✅ Configurado" : "❌ Não configurado"}`);

	const testEmail = "<EMAIL>";

	try {
		console.log("\n📧 Gerando magic link...");

		// Gerar magic link usando Better Auth
		const result = await auth.api.signInMagicLink({
			body: {
				email: testEmail,
				callbackURL: `${getBaseUrl()}/app`,
			},
			headers: new Headers(),
		});

		console.log("✅ Magic link gerado com sucesso");
		console.log("📋 Resultado:", result);

		console.log("\n🔗 URL do magic link de teste:");
		console.log(`${getBaseUrl()}/api/auth/magic-link/verify?token=TEST_TOKEN&callbackURL=${encodeURIComponent(`${getBaseUrl()}/app`)}`);

		console.log("\n📝 Para testar:");
		console.log("   1. Acesse a URL acima no navegador");
		console.log("   2. Verifique se redireciona corretamente");
		console.log("   3. Verifique se a sessão é criada");

		console.log("\n🔍 Problemas identificados:");
		console.log("   - Token inválido: O token pode ter expirado ou sido gerado em ambiente diferente");
		console.log("   - BETTER_AUTH_SECRET: Deve ser o mesmo entre desenvolvimento e produção");
		console.log("   - Base URL: Deve corresponder ao ambiente onde o token foi gerado");

	} catch (error) {
		console.error("❌ Erro ao testar magic link:", error);
	}
}

testMagicLink().catch(console.error);
