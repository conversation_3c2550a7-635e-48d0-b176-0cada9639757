#!/usr/bin/env tsx

import { getBaseUrl } from "@repo/utils";

async function testDirectLogin() {
  console.log("🔐 Testando login direto via HTTP...\n");

  const baseUrl = getBaseUrl();
  const testEmail = "<EMAIL>";
  const testPassword = "TestPassword123!";

  console.log(`🌐 Base URL: ${baseUrl}`);
  console.log(`📧 Email: ${testEmail}`);
  console.log(`🔑 Senha: ${testPassword}\n`);

  // 1. Testar endpoint de sessão primeiro
  console.log("1. 🔍 Testando endpoint de sessão...");
  try {
    const sessionResponse = await fetch(`${baseUrl}/api/auth/session`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`   Status: ${sessionResponse.status} ${sessionResponse.statusText}`);
    
    if (sessionResponse.ok) {
      const sessionData = await sessionResponse.json();
      console.log(`   Resposta: ${JSON.stringify(sessionData, null, 2)}`);
    } else {
      const errorText = await sessionResponse.text();
      console.log(`   Erro: ${errorText}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro na requisição: ${error}`);
  }

  // 2. Testar login com email/senha
  console.log("\n2. 🔐 Testando login com email/senha...");
  try {
    const loginResponse = await fetch(`${baseUrl}/api/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword
      })
    });

    console.log(`   Status: ${loginResponse.status} ${loginResponse.statusText}`);
    
    // Verificar headers de resposta
    console.log("   Headers de resposta:");
    loginResponse.headers.forEach((value, key) => {
      console.log(`     ${key}: ${value}`);
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log(`   ✅ Login bem-sucedido!`);
      console.log(`   Resposta: ${JSON.stringify(loginData, null, 2)}`);
    } else {
      const errorText = await loginResponse.text();
      console.log(`   ❌ Erro no login: ${errorText}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro na requisição: ${error}`);
  }

  // 3. Testar com credenciais inválidas para comparar
  console.log("\n3. 🚫 Testando com credenciais inválidas...");
  try {
    const invalidResponse = await fetch(`${baseUrl}/api/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "wrongpassword"
      })
    });

    console.log(`   Status: ${invalidResponse.status} ${invalidResponse.statusText}`);
    
    if (!invalidResponse.ok) {
      const errorText = await invalidResponse.text();
      console.log(`   Erro esperado: ${errorText}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro na requisição: ${error}`);
  }

  // 4. Testar endpoint de magic link para comparar
  console.log("\n4. 📧 Testando magic link para comparar...");
  try {
    const magicResponse = await fetch(`${baseUrl}/api/auth/sign-in/magic-link`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        callbackURL: "/app"
      })
    });

    console.log(`   Status: ${magicResponse.status} ${magicResponse.statusText}`);
    
    if (magicResponse.ok) {
      const magicData = await magicResponse.json();
      console.log(`   ✅ Magic link enviado!`);
      console.log(`   Resposta: ${JSON.stringify(magicData, null, 2)}`);
    } else {
      const errorText = await magicResponse.text();
      console.log(`   ❌ Erro no magic link: ${errorText}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro na requisição: ${error}`);
  }

  console.log("\n📋 Resumo dos Testes:");
  console.log("- Se o endpoint de sessão responde, o Better Auth está funcionando");
  console.log("- Se o magic link funciona mas o login não, o problema é específico do email/senha");
  console.log("- Se ambos falham, o problema é mais geral na configuração");
  
  console.log("\n✅ Teste direto concluído!");
}

// Executar teste
testDirectLogin()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Erro no teste:", error);
    process.exit(1);
  });
