#!/usr/bin/env tsx

import { getBaseUrl } from "@repo/utils";

async function testExistingUser() {
  console.log("🔐 Testando usuários existentes...\n");

  const baseUrl = getBaseUrl();
  console.log(`🌐 Base URL: ${baseUrl}\n`);

  // Credenciais dos usuários que já existem no sistema
  const testUsers = [
    { email: "<EMAIL>", password: "admin123" },
    { email: "<EMAIL>", password: "user123" },
    { email: "<EMAIL>", password: "user123" }
  ];

  for (const user of testUsers) {
    console.log(`🧪 Testando usuário: ${user.email}`);
    
    try {
      const loginResponse = await fetch(`${baseUrl}/api/auth/sign-in/email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.email,
          password: user.password
        })
      });

      console.log(`   Status: ${loginResponse.status} ${loginResponse.statusText}`);
      
      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        console.log(`   ✅ Login bem-sucedido!`);
        console.log(`   Resposta: ${JSON.stringify(loginData, null, 2)}`);
      } else {
        const errorText = await loginResponse.text();
        console.log(`   ❌ Erro no login: ${errorText}`);
      }
    } catch (error) {
      console.log(`   ❌ Erro na requisição: ${error}`);
    }
    
    console.log(''); // Linha em branco
  }

  // Testar também com senhas diferentes
  console.log("🔍 Testando senhas alternativas para admin...");
  const alternativePasswords = ["admin", "password", "123456", "admin@123"];
  
  for (const password of alternativePasswords) {
    try {
      const loginResponse = await fetch(`${baseUrl}/api/auth/sign-in/email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: "<EMAIL>",
          password: password
        })
      });

      if (loginResponse.ok) {
        console.log(`   ✅ Senha correta encontrada: "${password}"`);
        const loginData = await loginResponse.json();
        console.log(`   Resposta: ${JSON.stringify(loginData, null, 2)}`);
        break;
      } else {
        console.log(`   ❌ Senha "${password}" não funciona`);
      }
    } catch (error) {
      console.log(`   ❌ Erro testando senha "${password}": ${error}`);
    }
  }

  console.log("\n✅ Teste de usuários existentes concluído!");
}

// Executar teste
testExistingUser()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Erro no teste:", error);
    process.exit(1);
  });
