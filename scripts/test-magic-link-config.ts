#!/usr/bin/env node

import { config } from "dotenv";
import { resolve } from "path";

// Carregar variáveis de ambiente do .env.local
config({ path: resolve(__dirname, "../.env.local") });

async function testMagicLinkConfig() {
	try {
		console.log("🧪 Testando configuração do magic link...\n");

		// Test 1: Verificar variáveis de ambiente
		console.log("1. 🔐 Variáveis de ambiente:");
		const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
		const vercelUrl = process.env.NEXT_PUBLIC_VERCEL_URL;
		const nodeEnv = process.env.NODE_ENV;
		const betterAuthSecret = process.env.BETTER_AUTH_SECRET;

		console.log(`   - NEXT_PUBLIC_SITE_URL: ${siteUrl || "❌ Não definido"}`);
		console.log(`   - NEXT_PUBLIC_VERCEL_URL: ${vercelUrl || "❌ Não definido"}`);
		console.log(`   - NODE_ENV: ${nodeEnv || "❌ Não definido"}`);
		console.log(`   - BETTER_AUTH_SECRET: ${betterAuthSecret ? "✅ Definido" : "❌ Não definido"}`);

		// Test 2: Verificar configuração de email
		console.log("\n2. 📧 Configuração de email:");
		const smtpHost = process.env.SMTP_HOST;
		const smtpUser = process.env.SMTP_USER;
		const smtpPass = process.env.SMTP_PASS;

		console.log(`   - SMTP_HOST: ${smtpHost || "❌ Não definido"}`);
		console.log(`   - SMTP_USER: ${smtpUser || "❌ Não definido"}`);
		console.log(`   - SMTP_PASS: ${smtpPass ? "✅ Definido" : "❌ Não definido"}`);

		// Test 3: Verificar URLs
		console.log("\n3. 🌐 URLs:");
		const productionUrl = "https://caktomembers.cloud.cakto.app";
		console.log(`   - URL de produção: ${productionUrl}`);
		console.log(`   - Magic Link Endpoint: ${productionUrl}/api/auth/magic-link/verify`);
		console.log(`   - Login Page: ${productionUrl}/auth/login`);

		// Test 4: Verificar se o domínio está configurado corretamente
		console.log("\n4. 🔍 Verificação de domínio:");
		if (siteUrl === productionUrl) {
			console.log("   ✅ NEXT_PUBLIC_SITE_URL está configurado corretamente para produção");
		} else {
			console.log("   ❌ NEXT_PUBLIC_SITE_URL não está configurado para produção");
			console.log(`   📝 Atual: ${siteUrl}`);
			console.log(`   📝 Esperado: ${productionUrl}`);
		}

		// Test 5: Verificar se o BETTER_AUTH_SECRET está definido
		console.log("\n5. 🔑 BETTER_AUTH_SECRET:");
		if (betterAuthSecret) {
			console.log("   ✅ BETTER_AUTH_SECRET está definido");
			console.log(`   📏 Tamanho: ${betterAuthSecret.length} caracteres`);
		} else {
			console.log("   ❌ BETTER_AUTH_SECRET não está definido");
			console.log("   💡 Isso pode causar problemas com tokens inválidos");
		}

		// Test 6: Verificar configuração de produção
		console.log("\n6. 🚀 Configuração de produção:");
		if (nodeEnv === "production") {
			console.log("   ✅ NODE_ENV está configurado como 'production'");
		} else {
			console.log(`   ⚠️  NODE_ENV está configurado como '${nodeEnv}'`);
			console.log("   💡 Para produção, deve ser 'production'");
		}

		console.log("\n✅ Teste de configuração concluído!");

		// Recomendações
		console.log("\n📋 Recomendações:");
		if (!betterAuthSecret) {
			console.log("   - Defina BETTER_AUTH_SECRET no ambiente de produção");
		}
		if (siteUrl !== productionUrl) {
			console.log("   - Configure NEXT_PUBLIC_SITE_URL para a URL de produção");
		}
		if (!smtpHost || !smtpUser || !smtpPass) {
			console.log("   - Configure as variáveis SMTP para envio de emails");
		}

	} catch (error) {
		console.error("❌ Erro durante o teste:", error);
	}
}

testMagicLinkConfig();
