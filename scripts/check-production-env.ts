#!/usr/bin/env node

import { config } from "dotenv";
import { resolve } from "path";

// Carregar variáveis de ambiente do .env.local
config({ path: resolve(__dirname, "../.env.local") });

async function checkProductionEnv() {
	try {
		console.log("🔍 Verificando variáveis de ambiente de produção...\n");

		// Test 1: Variáveis SMTP
		console.log("1. 📧 Configuração SMTP:");
		const mailHost = process.env.MAIL_HOST;
		const mailUser = process.env.MAIL_USER;
		const mailPass = process.env.MAIL_PASS;
		const smtpHost = process.env.SMTP_HOST;
		const smtpUser = process.env.SMTP_USER;
		const smtpPass = process.env.SMTP_PASS;

		console.log(`   - MAIL_HOST: ${mailHost || "❌ Não definido"}`);
		console.log(`   - MAIL_USER: ${mailUser || "❌ Não definido"}`);
		console.log(`   - MAIL_PASS: ${mailPass ? "✅ Definido" : "❌ Não definido"}`);
		console.log(`   - SMTP_HOST: ${smtpHost || "❌ Não definido"}`);
		console.log(`   - SMTP_USER: ${smtpUser || "❌ Não definido"}`);
		console.log(`   - SMTP_PASS: ${smtpPass ? "✅ Definido" : "❌ Não definido"}`);

		// Test 2: SSO Configuration
		console.log("\n2. 🔐 Configuração SSO:");
		const ssoRedirectUri = process.env.SSO_REDIRECT_URI;
		const caktoApiUrl = process.env.CAKTO_API_URL;
		const caktoClientId = process.env.CAKTO_CLIENT_ID;
		const caktoClientSecret = process.env.CAKTO_CLIENT_SECRET;

		console.log(`   - SSO_REDIRECT_URI: ${ssoRedirectUri || "❌ Não definido"}`);
		console.log(`   - CAKTO_API_URL: ${caktoApiUrl || "❌ Não definido"}`);
		console.log(`   - CAKTO_CLIENT_ID: ${caktoClientId ? "✅ Definido" : "❌ Não definido"}`);
		console.log(`   - CAKTO_CLIENT_SECRET: ${caktoClientSecret ? "✅ Definido" : "❌ Não definido"}`);

		// Test 3: URLs e Domínios
		console.log("\n3. 🌐 URLs e Domínios:");
		const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
		const appUrl = process.env.NEXT_PUBLIC_APP_URL;
		const nodeEnv = process.env.NODE_ENV;

		console.log(`   - NEXT_PUBLIC_SITE_URL: ${siteUrl || "❌ Não definido"}`);
		console.log(`   - NEXT_PUBLIC_APP_URL: ${appUrl || "❌ Não definido"}`);
		console.log(`   - NODE_ENV: ${nodeEnv || "❌ Não definido"}`);

		// Test 4: Configurações de Produção
		console.log("\n4. 🚀 Configurações de Produção:");
		const useCaktoMock = process.env.USE_CAKTO_MOCK;
		const betterAuthSecret = process.env.BETTER_AUTH_SECRET;

		console.log(`   - USE_CAKTO_MOCK: ${useCaktoMock || "❌ Não definido"}`);
		console.log(`   - BETTER_AUTH_SECRET: ${betterAuthSecret ? "✅ Definido" : "❌ Não definido"}`);

		// Análise e Recomendações
		console.log("\n📋 Análise e Recomendações:");

		// Verificar problemas SMTP
		if (mailHost && !smtpHost) {
			console.log("   ⚠️  MAIL_HOST definido mas SMTP_HOST não - pode causar problemas");
		}
		if (mailUser && !smtpUser) {
			console.log("   ⚠️  MAIL_USER definido mas SMTP_USER não - pode causar problemas");
		}
		if (mailPass && !smtpPass) {
			console.log("   ⚠️  MAIL_PASS definido mas SMTP_PASS não - pode causar problemas");
		}

		// Verificar SSO_REDIRECT_URI
		if (ssoRedirectUri && ssoRedirectUri.includes("localhost")) {
			console.log("   ❌ SSO_REDIRECT_URI contém localhost - deve ser URL de produção");
		}

		// Verificar USE_CAKTO_MOCK
		if (useCaktoMock === "true") {
			console.log("   ❌ USE_CAKTO_MOCK=true em produção - deve ser false");
		}

		// Verificar NODE_ENV
		if (nodeEnv !== "production") {
			console.log("   ⚠️  NODE_ENV não é 'production'");
		}

		// Verificar URLs
		if (siteUrl !== "https://caktomembers.cloud.cakto.app") {
			console.log("   ⚠️  NEXT_PUBLIC_SITE_URL não está configurado para produção");
		}

		console.log("\n🔧 Correções Necessárias:");

		if (mailHost && !smtpHost) {
			console.log("   - Adicionar: SMTP_HOST=" + mailHost);
		}
		if (mailUser && !smtpUser) {
			console.log("   - Adicionar: SMTP_USER=" + mailUser);
		}
		if (mailPass && !smtpPass) {
			console.log("   - Adicionar: SMTP_PASS=" + mailPass);
		}
		if (ssoRedirectUri && ssoRedirectUri.includes("localhost")) {
			console.log("   - Corrigir: SSO_REDIRECT_URI=https://caktomembers.cloud.cakto.app/auth/sso/callback");
		}
		if (useCaktoMock === "true") {
			console.log("   - Corrigir: USE_CAKTO_MOCK=false");
		}

		console.log("\n✅ Verificação concluída!");

	} catch (error) {
		console.error("❌ Erro durante a verificação:", error);
	}
}

checkProductionEnv();
