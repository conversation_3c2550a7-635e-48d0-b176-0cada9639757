#!/usr/bin/env node

import { config } from "dotenv";
import { resolve } from "path";
import { auth } from "../packages/auth";

// Carregar variáveis de ambiente do .env.local
config({ path: resolve(__dirname, "../.env.local") });

async function testMagicLinkProduction() {
	try {
		console.log("🧪 Testando magic link em produção...\n");

		// Test 1: Verificar configuração do Better Auth
		console.log("1. ✅ Configuração do Better Auth:");
		console.log(`   - Base URL: ${auth.options.baseURL}`);
		console.log(`   - Trusted Origins: ${auth.options.trustedOrigins?.join(", ")}`);
		console.log(`   - App Name: ${auth.options.appName}`);

		// Test 2: Verificar plugins
		console.log("\n2. 📦 Plugins carregados:");
		const plugins = auth.options.plugins || [];
		const pluginNames = plugins.map((plugin: any) => plugin.id || plugin.name || "unknown");
		console.log(`   - Plugins: ${pluginNames.join(", ")}`);

		// Test 3: Verificar se magic link está habilitado
		const magicLinkPlugin = plugins.find((plugin: any) => plugin.id === "magic-link");
		if (magicLinkPlugin) {
			console.log("   - Magic Link: ✅ Habilitado");
		} else {
			console.log("   - Magic Link: ❌ Não encontrado");
		}

		// Test 4: Verificar variáveis de ambiente
		console.log("\n3. 🔐 Variáveis de ambiente:");
		const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
		const vercelUrl = process.env.NEXT_PUBLIC_VERCEL_URL;
		const nodeEnv = process.env.NODE_ENV;

		console.log(`   - NEXT_PUBLIC_SITE_URL: ${siteUrl || "❌ Não definido"}`);
		console.log(`   - NEXT_PUBLIC_VERCEL_URL: ${vercelUrl || "❌ Não definido"}`);
		console.log(`   - NODE_ENV: ${nodeEnv || "❌ Não definido"}`);

		// Test 5: Gerar um magic link de teste
		console.log("\n4. 📧 Gerando magic link de teste...");
		const testEmail = "<EMAIL>";

		const result = await auth.api.signInMagicLink({
			body: {
				email: testEmail,
				callbackURL: "/app"
			},
			headers: {}
		});

		if (result.status) {
			console.log("   ✅ Magic link gerado com sucesso");
			console.log(`   📧 Email: ${testEmail}`);
			console.log(`   🔗 Callback URL: /app`);
		} else {
			console.log("   ❌ Erro ao gerar magic link");
			console.log(`   📝 Erro: ${JSON.stringify(result, null, 2)}`);
		}

		// Test 6: Verificar URL de produção
		console.log("\n5. 🌐 URL de produção:");
		const productionUrl = "https://caktomembers.cloud.cakto.app";
		console.log(`   - URL: ${productionUrl}`);
		console.log(`   - Magic Link Endpoint: ${productionUrl}/api/auth/magic-link/verify`);

		console.log("\n✅ Teste concluído!");

	} catch (error) {
		console.error("❌ Erro durante o teste:", error);
	}
}

testMagicLinkProduction();
