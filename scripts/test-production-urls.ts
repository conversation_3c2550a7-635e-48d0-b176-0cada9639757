#!/usr/bin/env tsx

import { getBaseUrl } from "@repo/utils";

async function testProductionUrls() {
  console.log("🧪 Testing URL configuration for production...\n");

  // Test current configuration
  console.log("1. 🔍 Current URL Configuration:");
  console.log(`   - NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`   - NEXT_PUBLIC_SITE_URL: ${process.env.NEXT_PUBLIC_SITE_URL || 'NOT SET'}`);
  console.log(`   - NEXT_PUBLIC_VERCEL_URL: ${process.env.NEXT_PUBLIC_VERCEL_URL || 'NOT SET'}`);
  console.log(`   - PORT: ${process.env.PORT || '3000'}`);

  // Test getBaseUrl function
  console.log("\n2. 📍 getBaseUrl() Results:");
  const baseUrl = getBaseUrl();
  console.log(`   - Current result: ${baseUrl}`);

  // Test different scenarios
  console.log("\n3. 🎯 Testing Different Scenarios:");
  
  // Scenario 1: Development
  const originalNodeEnv = process.env.NODE_ENV;
  const originalSiteUrl = process.env.NEXT_PUBLIC_SITE_URL;
  
  process.env.NODE_ENV = 'development';
  delete process.env.NEXT_PUBLIC_SITE_URL;
  console.log(`   - Development (no SITE_URL): ${getBaseUrl()}`);
  
  // Scenario 2: Production without SITE_URL
  process.env.NODE_ENV = 'production';
  delete process.env.NEXT_PUBLIC_SITE_URL;
  console.log(`   - Production (no SITE_URL): ${getBaseUrl()}`);
  
  // Scenario 3: With explicit SITE_URL
  process.env.NEXT_PUBLIC_SITE_URL = 'https://caktomembers.cloud.cakto.app';
  console.log(`   - With explicit SITE_URL: ${getBaseUrl()}`);
  
  // Scenario 4: Vercel deployment
  process.env.NEXT_PUBLIC_VERCEL_URL = 'members-base.vercel.app';
  delete process.env.NEXT_PUBLIC_SITE_URL;
  console.log(`   - Vercel deployment: ${getBaseUrl()}`);

  // Restore original values
  if (originalNodeEnv) process.env.NODE_ENV = originalNodeEnv;
  if (originalSiteUrl) process.env.NEXT_PUBLIC_SITE_URL = originalSiteUrl;
  delete process.env.NEXT_PUBLIC_VERCEL_URL;

  console.log("\n4. 🔧 Configuration Recommendations:");
  
  if (process.env.NODE_ENV === 'production' && !process.env.NEXT_PUBLIC_SITE_URL) {
    console.log("   ⚠️  PRODUCTION: Set NEXT_PUBLIC_SITE_URL in your deployment environment");
    console.log("   📝 Example: NEXT_PUBLIC_SITE_URL=https://caktomembers.cloud.cakto.app");
  } else if (process.env.NEXT_PUBLIC_SITE_URL) {
    console.log("   ✅ NEXT_PUBLIC_SITE_URL is configured correctly");
  } else {
    console.log("   ✅ Development configuration looks good");
  }

  console.log("\n5. 🌐 Magic Link URL Test:");
  console.log(`   - Magic links will be sent with base URL: ${baseUrl}`);
  console.log(`   - Magic link verification endpoint: ${baseUrl}/api/auth/magic-link/verify`);
  console.log(`   - SSO callback endpoint: ${baseUrl}/api/auth/oauth2/callback/django-sso`);

  console.log("\n6. 📋 Deployment Checklist:");
  console.log("   □ Set NEXT_PUBLIC_SITE_URL in production environment");
  console.log("   □ Update SSO_REDIRECT_URI to match production domain");
  console.log("   □ Configure CORS/trusted origins if needed");
  console.log("   □ Test magic link emails in production");
  console.log("   □ Test SSO flow in production");

  console.log("\n✅ URL configuration test completed!");
}

// Run the test
testProductionUrls()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
