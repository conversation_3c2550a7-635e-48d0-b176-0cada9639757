#!/usr/bin/env tsx

import { db } from "@repo/database";
import { auth } from "../packages/auth";

async function debugPasswordHash() {
  console.log("🔍 Debugando hash de senha...\n");

  const testEmail = "<EMAIL>";
  const testPassword = "TestPassword123!";

  try {
    // 1. Buscar usuário e conta
    const user = await db.user.findUnique({
      where: { email: testEmail },
      include: {
        accounts: {
          where: {
            providerId: "credential"
          }
        }
      }
    });

    if (!user) {
      console.log("❌ Usuário não encontrado");
      return;
    }

    console.log("👤 Usuário encontrado:");
    console.log(`   - ID: ${user.id}`);
    console.log(`   - Email: ${user.email}`);
    console.log(`   - Email Verificado: ${user.emailVerified}`);

    const credentialAccount = user.accounts[0];
    if (!credentialAccount) {
      console.log("❌ Conta credential não encontrada");
      return;
    }

    console.log("\n🔐 Conta Credential:");
    console.log(`   - ID: ${credentialAccount.id}`);
    console.log(`   - Provider ID: ${credentialAccount.providerId}`);
    console.log(`   - Account ID: ${credentialAccount.accountId}`);
    console.log(`   - Senha Hash: ${credentialAccount.password ? "Existe" : "Não existe"}`);
    
    if (credentialAccount.password) {
      console.log(`   - Hash Length: ${credentialAccount.password.length}`);
      console.log(`   - Hash Preview: ${credentialAccount.password.substring(0, 50)}...`);
    }

    // 2. Testar verificação de senha usando Better Auth
    console.log("\n🧪 Testando verificação de senha...");
    
    if (credentialAccount.password) {
      try {
        const authContext = await auth.$context;
        const isValid = await authContext.password.verify(testPassword, credentialAccount.password);
        console.log(`   - Senha válida: ${isValid ? "✅ SIM" : "❌ NÃO"}`);
      } catch (error) {
        console.log(`   - Erro na verificação: ${error}`);
      }
    }

    // 3. Criar novo hash e comparar
    console.log("\n🔄 Criando novo hash para comparar...");
    try {
      const authContext = await auth.$context;
      const newHash = await authContext.password.hash(testPassword);
      console.log(`   - Novo hash criado: ${newHash.substring(0, 50)}...`);
      
      const isNewHashValid = await authContext.password.verify(testPassword, newHash);
      console.log(`   - Novo hash válido: ${isNewHashValid ? "✅ SIM" : "❌ NÃO"}`);
      
      // Comparar hashes
      console.log(`   - Hashes são iguais: ${credentialAccount.password === newHash ? "✅ SIM" : "❌ NÃO"}`);
    } catch (error) {
      console.log(`   - Erro ao criar novo hash: ${error}`);
    }

    // 4. Testar login direto usando Better Auth API
    console.log("\n🔑 Testando login direto via Better Auth API...");
    try {
      const loginResult = await auth.api.signInEmail({
        body: {
          email: testEmail,
          password: testPassword
        }
      });

      if (loginResult.data) {
        console.log("   ✅ Login bem-sucedido via API!");
        console.log(`   - User: ${loginResult.data.user.email}`);
        console.log(`   - Session: ${loginResult.data.session ? "Criada" : "Não criada"}`);
      } else {
        console.log(`   ❌ Erro no login: ${loginResult.error?.message || "Erro desconhecido"}`);
        console.log(`   - Error Code: ${loginResult.error?.code || "N/A"}`);
      }
    } catch (error) {
      console.log(`   ❌ Erro na API: ${error}`);
    }

    // 5. Verificar se há outros usuários com senhas funcionando
    console.log("\n👥 Verificando outros usuários...");
    const usersWithPasswords = await db.user.findMany({
      include: {
        accounts: {
          where: {
            providerId: "credential",
            password: {
              not: null
            }
          }
        }
      },
      take: 3
    });

    console.log(`   - Usuários com senhas: ${usersWithPasswords.filter(u => u.accounts.length > 0).length}`);
    usersWithPasswords.forEach(user => {
      if (user.accounts.length > 0) {
        console.log(`     • ${user.email} - Hash: ${user.accounts[0].password?.substring(0, 20)}...`);
      }
    });

  } catch (error) {
    console.error("❌ Erro no debug:", error);
  }
}

// Executar debug
debugPasswordHash()
  .then(() => {
    console.log("\n✅ Debug concluído!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Erro:", error);
    process.exit(1);
  });
