import { getOrganizationBySlug } from "@repo/database";
import { db } from "@repo/database";
import slugify from "@sindresorhus/slugify";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { nanoid } from "nanoid";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { domainsRouter } from "./domains";
import { getOrganizationStats } from "./get-organization-stats";

export const organizationsRouter = new Hono()
	.basePath("/organizations")
	.route("/", domainsRouter)
	.route("/", getOrganizationStats)
	.get(
		"/generate-slug",
		validator(
			"query",
			z.object({
				name: z.string(),
			}),
		),
		describeRoute({
			summary: "Generate a slug for an organization",
			tags: ["Organizations"],
		}),
		async (c) => {
			const { name } = c.req.valid("query");

			const baseSlug = slugify(name, {
				lowercase: true,
			});

			let slug = baseSlug;
			let hasAvailableSlug = false;

			for (let i = 0; i < 3; i++) {
				const existing = await getOrganizationBySlug(slug);

				if (!existing) {
					hasAvailableSlug = true;
					break;
				}

				slug = `${baseSlug}-${nanoid(5)}`;
			}

			if (!hasAvailableSlug) {
				return c.json(
					{
						error: "No available slug found",
					},
					400,
				);
			}

			return c.json({
				slug,
			});
		},
	)
	.use(authMiddleware)
	.get(
		"/user",
		describeRoute({
			summary: "Get user's organizations",
			tags: ["Organizations"],
		}),
		async (c) => {
			try {
				const user = c.get("user");


				const memberships = await db.member.findMany({
					where: {
						userId: user.id,
					},
					include: {
						organization: {
							select: {
								id: true,
								name: true,
								slug: true,
								logo: true,
							},
						},
					},
					orderBy: {
						organization: {
							name: "asc",
						},
					},
				});


				const organizations = memberships.map((membership) => ({
					id: membership.organization.id,
					name: membership.organization.name,
					slug: membership.organization.slug,
					logo: membership.organization.logo,
					role: membership.role,
				}));

				return c.json(organizations);
			} catch (error) {
				console.error("Error fetching user organizations:", error);
				return c.json({ error: "Internal server error" }, 500);
			}
		},
	);
