import { auth } from "@repo/auth";
import { Hono } from "hono";

export const authRouter = new Hono()
	.get("/auth/test", (c) => {
		console.log("🔍 Auth Router - Test route hit!");
		return c.json({ message: "Auth router test working!" });
	})
	.post("/auth/test-post", (c) => {
		console.log("🔍 Auth Router - POST test route hit!");
		return c.json({ message: "Auth router POST test working!" });
	})
	.get("/auth/get-session", (c) => {
		console.log("🔍 Auth Router - GET get-session request to:", c.req.url);
		return auth.handler(c.req.raw);
	})
	.post("/auth/sign-in/email", (c) => {
		console.log("🔍 Auth Router - POST sign-in/email request to:", c.req.url);
		return auth.handler(c.req.raw);
	})
	.get("/auth/**", (c) => {
		console.log("🔍 Auth Router - GET wildcard request to:", c.req.url);
		return auth.handler(c.req.raw);
	})
	.post("/auth/**", (c) => {
		console.log("🔍 Auth Router - POST wildcard request to:", c.req.url);
		return auth.handler(c.req.raw);
	});
