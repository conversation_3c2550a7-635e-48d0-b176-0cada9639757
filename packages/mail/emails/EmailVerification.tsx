import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface EmailVerificationProps {
  name?: string;
  url: string;
}

export const EmailVerification = ({ name, url }: EmailVerificationProps) => (
  <Wrapper>
    <Preview>Confirme seu email | Cakto Members</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 22, margin: "24px 0 8px" }}>
        Confirme seu endereço de email
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0" }}>
        Olá{ name ? ` ${name}` : "" },<br />
        Para ativar sua conta, clique no botão abaixo e confirme seu endereço de email.
      </Text>
      <Section style={{ margin: "32px 0" }}>
        <Link
          href={url}
          style={{
            display: "inline-block",
            background: "#0F7864",
            color: "#fff",
            padding: "12px 32px",
            borderRadius: 6,
            fontWeight: 600,
            textDecoration: "none",
            fontSize: 16,
          }}
        >
          Confirmar Email
        </Link>
      </Section>
      <Text style={{ color: "#555", fontSize: 14, margin: "24px 0 0" }}>
        Se você não solicitou este cadastro, ignore este email.
      </Text>
    </Section>
  </Wrapper>
);

export default EmailVerification;
