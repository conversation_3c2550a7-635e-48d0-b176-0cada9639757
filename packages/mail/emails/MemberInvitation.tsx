import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface MemberInvitationProps {
  name?: string;
  organizationName: string;
  inviterName?: string;
  url: string;
}

export const MemberInvitation = ({
  name,
  organizationName,
  inviterName,
  url
}: MemberInvitationProps) => (
  <Wrapper>
    <Preview>Convite para ser membro de {organizationName} | Cakto Members</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 22, margin: "24px 0 8px" }}>
        Convite para ser membro
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0" }}>
        Olá{ name ? ` ${name}` : "" },<br />
        {inviterName ? `${inviterName} ` : ""}convidou você para ser membro da organização <strong>{organizationName}</strong>.<br />
        Aceite o convite para acessar cursos exclusivos e conteúdo personalizado.
      </Text>
      <Section style={{ margin: "32px 0" }}>
        <Link
          href={url}
          style={{
            display: "inline-block",
            background: "#0F7864",
            color: "#fff",
            padding: "12px 32px",
            borderRadius: 6,
            fontWeight: 600,
            textDecoration: "none",
            fontSize: 16,
          }}
        >
          Aceitar Convite
        </Link>
      </Section>
      <Text style={{ color: "#555", fontSize: 14, margin: "24px 0 0" }}>
        Como membro, você terá acesso a cursos exclusivos e suporte personalizado.
      </Text>
    </Section>
  </Wrapper>
);

export default MemberInvitation;
