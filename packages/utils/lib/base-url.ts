export function getBaseUrl() {
	// 1. Prioridade máxima: NEXT_PUBLIC_SITE_URL sempre tem precedência
	if (process.env.NEXT_PUBLIC_SITE_URL) {
		return process.env.NEXT_PUBLIC_SITE_URL;
	}

	// 2. Vercel deployment
	if (process.env.NEXT_PUBLIC_VERCEL_URL) {
		return `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`;
	}

	// 3. Produção: usar URL de produção conhecida
	if (process.env.NODE_ENV === "production") {
		return "https://caktomembers.cloud.cakto.app";
	}

	// 4. Desenvolvimento: localhost
	return `http://localhost:${process.env.PORT ?? 3000}`;
}
